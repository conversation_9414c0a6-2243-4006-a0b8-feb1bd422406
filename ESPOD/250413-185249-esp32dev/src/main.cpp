#include <Arduino.h>
#include "animations/Breather.h"
#include "audio/AudioManager.h"
#include "ui/Buttons.h"
#include "core/DeviceState.h"
#include "utils/sleeper.h"

DeviceState currentState = INITIALIZING;  // Start in INITIALIZING state

// Function declarations
void onButtonPress(button_id_t button);
void onLongPress(button_id_t button);
void renderInitializing();
void renderSearchingTWS();
void renderConnecting();
void renderConnectedState();
void renderBrowsingFiles();
void renderPlayingState();
void renderPausedState();
void renderStoppedState();

// Audio manager callbacks
void onConnectionChanged(bool connected, const char* device_name);
void onPlaybackChanged(AudioState state, const char* filename);

void setup() {
  Serial.begin(115200);
  Serial.println("\n\n=== ESPOD Bluetooth A2DP MP3 Player ===\n");
  // Delay for Serial to initialize
  delay(1000);

  modifyJoystickForNormalUse();
  initBreather();
  initButtons(onButtonPress, onLongPress);

  // Initialize Audio Manager
  if (audioManager.init(5, "ESPOD")) {
    Serial.println("AudioManager initialized successfully");
    audioManager.setConnectionCallback(onConnectionChanged);
    audioManager.setPlaybackCallback(onPlaybackChanged);
    currentState = SEARCHING_TWS;
  } else {
    Serial.println("Failed to initialize AudioManager");
    currentState = OFF;
  }
}

void loop() {
  // Always call audio manager loop for audio processing
  audioManager.loop();

  switch (currentState) {
    case OFF:
      sleepWell();
      break;

    case INITIALIZING:
      renderInitializing();
      break;

    case SEARCHING_TWS:
      renderSearchingTWS();
      break;

    case CONNECTING:
      renderConnecting();
      break;

    case CONNECTED:
      renderConnectedState();
      break;

    case BROWSING_FILES:
      renderBrowsingFiles();
      break;

    case PLAYING:
      renderPlayingState();
      break;

    case PAUSED:
      renderPausedState();
      break;

    case STOPPED:
      renderStoppedState();
      break;

    default:
      Serial.print("Unknown state: ");
      Serial.println(currentState);
      currentState = SEARCHING_TWS; // Reset to a known state
  }

  delay(100);  // Keep the program running
}

// Render initializing state
void renderInitializing() {
  Serial.println("Initializing audio system...");
  delay(1000);
}

// Render searching TWS state
void renderSearchingTWS() {
  Serial.println("Searching for TWS devices...");
  audioManager.startDiscovery();
  delay(2000);
}

// Render connecting state
void renderConnecting() {
  Serial.println("Connecting to device...");
  delay(1000);
}

// Render connected state
void renderConnectedState() {
  Serial.println("Connected to: " + audioManager.getConnectedDeviceName());
  Serial.printf("Files available: %d\n", audioManager.getFileCount());

  // Check if still connected
  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  } else {
    // Auto-transition to browsing files if we have files
    if (audioManager.getFileCount() > 0) {
      currentState = BROWSING_FILES;
    }
  }

  delay(2000);
}

// Render browsing files state
void renderBrowsingFiles() {
  Serial.printf("Browsing files (%d/%d): %s\n",
                audioManager.getCurrentFileIndex() + 1,
                audioManager.getFileCount(),
                audioManager.getCurrentFile().c_str());
  delay(1000);
}

// Render playing state
void renderPlayingState() {
  Serial.printf("Playing: %s\n", audioManager.getCurrentFile().c_str());

  // Check if still connected
  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

// Render paused state
void renderPausedState() {
  Serial.printf("Paused: %s\n", audioManager.getCurrentFile().c_str());

  // Check if still connected
  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

// Render stopped state
void renderStoppedState() {
  Serial.printf("Stopped: %s\n", audioManager.getCurrentFile().c_str());

  // Check if still connected
  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

// Callback function to handle button press
void onButtonPress(button_id_t button) {
  Serial.print("Button Pressed: ");
  Serial.println(button);

  switch (currentState) {
    case OFF:
      // Only RST long press can wake up from OFF state
      break;

    case INITIALIZING:
      // No button actions during initialization
      break;

    case SEARCHING_TWS:
      if (button == BTN_CENTER) {
        // Try to connect to any discovered device
        audioManager.connectToDevice("TWS");
        currentState = CONNECTING;
      }
      break;

    case CONNECTING:
      // No button actions during connecting
      break;

    case CONNECTED:
      if (button == BTN_CENTER) {
        // Go to file browsing
        currentState = BROWSING_FILES;
      }
      break;

    case BROWSING_FILES:
      if (button == BTN_UP) {
        // Previous file
        audioManager.previousTrack();
      } else if (button == BTN_DOWN) {
        // Next file
        audioManager.nextTrack();
      } else if (button == BTN_CENTER) {
        // Start playing selected file
        audioManager.play();
        currentState = PLAYING;
      }
      break;

    case PLAYING:
      if (button == BTN_UP) {
        // Volume up
        int vol = audioManager.getVolume();
        audioManager.setVolume(min(100, vol + 10));
      } else if (button == BTN_DOWN) {
        // Volume down
        int vol = audioManager.getVolume();
        audioManager.setVolume(max(0, vol - 10));
      } else if (button == BTN_LEFT) {
        // Previous track
        audioManager.previousTrack();
      } else if (button == BTN_RIGHT) {
        // Next track
        audioManager.nextTrack();
      } else if (button == BTN_CENTER) {
        // Pause
        audioManager.pause();
        currentState = PAUSED;
      }
      break;

    case PAUSED:
      if (button == BTN_UP) {
        // Volume up
        int vol = audioManager.getVolume();
        audioManager.setVolume(min(100, vol + 10));
      } else if (button == BTN_DOWN) {
        // Volume down
        int vol = audioManager.getVolume();
        audioManager.setVolume(max(0, vol - 10));
      } else if (button == BTN_LEFT) {
        // Previous track
        audioManager.previousTrack();
      } else if (button == BTN_RIGHT) {
        // Next track
        audioManager.nextTrack();
      } else if (button == BTN_CENTER) {
        // Resume playing
        audioManager.play();
        currentState = PLAYING;
      }
      break;

    case STOPPED:
      if (button == BTN_UP) {
        // Volume up
        int vol = audioManager.getVolume();
        audioManager.setVolume(min(100, vol + 10));
      } else if (button == BTN_DOWN) {
        // Volume down
        int vol = audioManager.getVolume();
        audioManager.setVolume(max(0, vol - 10));
      } else if (button == BTN_LEFT) {
        // Previous track
        audioManager.previousTrack();
      } else if (button == BTN_RIGHT) {
        // Next track
        audioManager.nextTrack();
      } else if (button == BTN_CENTER) {
        // Start playing
        audioManager.play();
        currentState = PLAYING;
      }
      break;

    default:
      Serial.println("Unknown state for button press");
  }
}

void onLongPress(button_id_t button) {
  Serial.printf("Long press detected on button: %d\n", button);

  if (button == BTN_RST) {
    if (currentState == OFF) {
      // Wake up from OFF state
      Serial.println("Waking up device...");
      currentState = SEARCHING_TWS;
    } else {
      // Turn off device from any other state
      Serial.println("Turning off device...");
      if (audioManager.isConnected()) {
        audioManager.disconnect();
      }
      currentState = OFF;
    }
  }
}

// Audio manager callback functions
void onConnectionChanged(bool connected, const char* device_name) {
  if (connected) {
    Serial.printf("Connected to: %s\n", device_name);
    currentState = CONNECTED;
  } else {
    Serial.println("Disconnected from device");
    currentState = SEARCHING_TWS;
  }
}

void onPlaybackChanged(AudioState state, const char* filename) {
  Serial.printf("Playback state changed: %d, file: %s\n", state, filename);

  switch (state) {
    case AUDIO_PLAYING:
      currentState = PLAYING;
      break;
    case AUDIO_PAUSED:
      currentState = PAUSED;
      break;
    case AUDIO_STOPPED:
      currentState = STOPPED;
      break;
    case AUDIO_CONNECTING:
      currentState = CONNECTING;
      break;
    case AUDIO_DISCONNECTED:
      currentState = SEARCHING_TWS;
      break;
  }
}